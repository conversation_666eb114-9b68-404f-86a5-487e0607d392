# Chater MCP 集成指南

本指南详细介绍如何在 Chater 中配置和使用 Model Context Protocol (MCP) 功能。

## 什么是 MCP？

Model Context Protocol (MCP) 是一个开放标准，允许 AI 模型通过标准化接口访问外部工具、数据源和服务。通过 MCP，Chater 可以：

- 访问文件系统
- 执行网络搜索
- 查询数据库
- 调用 API 服务
- 运行自定义工具

## 快速开始

### 1. 配置第一个 MCP 服务器

```bash
# 启动 MCP 配置
chater config mcp

# 选择 "添加新的 MCP 服务器"
# 按提示输入配置信息
```

### 2. 验证连接

```bash
# 查看所有服务器状态
chater mcp --status

# 测试特定服务器
chater mcp --test <server-id>
```

### 3. 开始使用

启动 Chater 后，MCP 工具会自动集成到 AI 的工具集中。

```bash
chater
```

## 详细配置

### 支持的传输类型

#### 1. Stdio 传输

最常用的传输方式，通过标准输入输出与 MCP 服务器通信。

**配置示例：**
```json
{
  "id": "filesystem",
  "name": "文件系统工具",
  "description": "提供文件读写功能",
  "transport": "stdio",
  "connection": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/projects"]
  },
  "enabled": true,
  "timeout": 10000,
  "retry": {
    "maxRetries": 3,
    "initialDelay": 1000,
    "maxDelay": 5000,
    "backoffMultiplier": 2
  }
}
```

**适用场景：**
- 本地工具和脚本
- 命令行程序
- 简单的服务器程序

#### 2. SSE (Server-Sent Events) 传输

通过 HTTP SSE 连接与 MCP 服务器通信。

**配置示例：**
```json
{
  "id": "web-search",
  "name": "网络搜索服务",
  "description": "提供网络搜索功能",
  "transport": "sse",
  "connection": {
    "type": "sse",
    "url": "http://localhost:3000/sse",
    "headers": {
      "Authorization": "Bearer your-api-key"
    }
  },
  "enabled": true
}
```

**适用场景：**
- Web 服务
- 云端 API
- 需要认证的服务

#### 3. WebSocket 传输

通过 WebSocket 连接与 MCP 服务器通信。

**配置示例：**
```json
{
  "id": "realtime-data",
  "name": "实时数据服务",
  "description": "提供实时数据查询",
  "transport": "websocket",
  "connection": {
    "type": "websocket",
    "url": "ws://localhost:3001/ws"
  },
  "enabled": true
}
```

**适用场景：**
- 实时数据服务
- 双向通信需求
- 高频率交互

### 高级配置选项

#### 重试策略

```json
{
  "retry": {
    "maxRetries": 3,        // 最大重试次数
    "initialDelay": 1000,   // 初始延迟 (毫秒)
    "maxDelay": 10000,      // 最大延迟 (毫秒)
    "backoffMultiplier": 2  // 退避倍数
  }
}
```

#### 超时设置

```json
{
  "timeout": 15000  // 连接超时时间 (毫秒)
}
```

#### 环境变量

```json
{
  "connection": {
    "type": "stdio",
    "command": "python",
    "args": ["mcp_server.py"],
    "env": {
      "API_KEY": "${API_KEY}",
      "DEBUG": "true"
    }
  }
}
```

## 常用 MCP 服务器

### 1. 文件系统服务器

提供文件读写功能。

**安装：**
```bash
npm install -g @modelcontextprotocol/server-filesystem
```

**配置：**
```json
{
  "id": "filesystem",
  "name": "文件系统",
  "transport": "stdio",
  "connection": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/allowed/directory"]
  },
  "enabled": true
}
```

**可用工具：**
- `read_file` - 读取文件内容
- `write_file` - 写入文件内容
- `list_directory` - 列出目录内容

### 2. Git 服务器

提供 Git 仓库操作功能。

**安装：**
```bash
npm install -g @modelcontextprotocol/server-git
```

**配置：**
```json
{
  "id": "git",
  "name": "Git 工具",
  "transport": "stdio",
  "connection": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-git", "/path/to/git/repo"]
  },
  "enabled": true
}
```

### 3. 数据库服务器

提供数据库查询功能。

**安装：**
```bash
npm install -g @modelcontextprotocol/server-sqlite
```

**配置：**
```json
{
  "id": "database",
  "name": "SQLite 数据库",
  "transport": "stdio",
  "connection": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/database.db"]
  },
  "enabled": true
}
```

## 管理命令

### 查看状态

```bash
# 显示所有服务器状态
chater mcp --status

# 列出服务器详情
chater mcp --list

# 显示可用工具
chater mcp --tools
```

### 连接管理

```bash
# 测试连接
chater mcp --test <server-id>

# 重新连接
chater mcp --reconnect <server-id>

# 交互式管理
chater mcp
```

### 配置管理

```bash
# 添加服务器
chater config mcp

# 编辑现有服务器
chater config mcp --edit <server-id>

# 删除服务器
chater config mcp --delete <server-id>
```

## 故障排除

### 常见问题

#### 1. 连接失败

**症状：** 服务器状态显示为 "错误" 或 "已断开"

**解决方案：**
1. 检查命令路径是否正确
2. 确认 MCP 服务器程序已安装
3. 验证参数配置
4. 查看错误日志

```bash
chater mcp --test <server-id>
```

#### 2. 工具不可用

**症状：** AI 无法使用 MCP 工具

**解决方案：**
1. 确认服务器已连接
2. 检查工具列表
3. 重新连接服务器

```bash
chater mcp --tools
chater mcp --reconnect <server-id>
```

#### 3. 权限问题

**症状：** 文件系统或其他资源访问被拒绝

**解决方案：**
1. 检查文件/目录权限
2. 确认路径配置正确
3. 验证环境变量设置

### 调试技巧

#### 1. 启用详细日志

在配置中添加调试选项：

```json
{
  "connection": {
    "env": {
      "DEBUG": "mcp:*"
    }
  }
}
```

#### 2. 手动测试 MCP 服务器

```bash
# 直接运行 MCP 服务器
npx @modelcontextprotocol/server-filesystem /path/to/directory

# 发送测试消息
echo '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}' | npx @modelcontextprotocol/server-filesystem /path/to/directory
```

#### 3. 检查网络连接

对于 SSE 和 WebSocket 传输：

```bash
# 测试 HTTP 连接
curl -N http://localhost:3000/sse

# 测试 WebSocket 连接
wscat -c ws://localhost:3001/ws
```

## 最佳实践

### 1. 安全考虑

- 限制文件系统访问路径
- 使用环境变量存储敏感信息
- 定期更新 MCP 服务器程序
- 监控连接状态和错误日志

### 2. 性能优化

- 合理设置超时时间
- 配置适当的重试策略
- 限制并发连接数
- 定期清理无用的服务器配置

### 3. 开发建议

- 为每个服务器提供清晰的描述
- 使用有意义的 ID 和名称
- 测试所有传输类型
- 编写详细的配置文档

## 扩展阅读

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [MCP 服务器列表](https://github.com/modelcontextprotocol/servers)
- [开发自定义 MCP 服务器](https://modelcontextprotocol.io/docs/building-servers)
