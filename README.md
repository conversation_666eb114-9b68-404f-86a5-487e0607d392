# Chater 🤖

一个智能的AI聊天代理CLI工具，基于LangChain框架构建，支持多种AI模型。

## 特性

- 🚀 **多模型支持**: DeepSeek、OpenAI GPT、Claude、Google Gemini
- 💬 **智能对话**: 基于LangChain的先进对话管理
- 🔌 **MCP 集成**: 支持 Model Context Protocol，扩展 AI 能力
- ⚙️ **灵活配置**: 简单的API密钥和偏好设置
- 🎨 **现代化界面**: 类似 Claude Code 的美观 CLI 体验
- 🧠 **智能补全**: 实时建议和内容补全
- 📊 **状态栏**: 项目信息、记忆状态、MCP 连接状态
- ✨ **视觉动画**: 流畅的加载和反馈效果
- 📝 **会话管理**: 支持多轮对话上下文
- 🔄 **向后兼容**: 传统界面完整保留

## 快速开始

### 安装

```bash
npm install -g chater
```

### 配置API密钥

```bash
chater config
```

选择要配置的AI服务商，输入对应的API密钥。

### 开始聊天

#### 默认启动 (现代化界面)
```bash
chater
```

#### 🎨 明确启用现代化界面
```bash
chater --modern
```

#### 🔧 传统界面
```bash
chater --classic
```

### 🎯 界面对比

| 特性 | 现代化界面 | 传统界面 |
|------|------------|----------|
| 视觉设计 | 🎨 类似 Claude Code | 🔧 简洁命令行 |
| 输入区域 | 📝 专门的输入框 | ⌨️ 标准提示符 |
| 智能补全 | 🧠 实时建议 | ❌ 无 |
| 状态显示 | 📊 丰富状态栏 | 💡 基础提示 |
| 动画效果 | ✨ 流畅动画 | ❌ 无 |
| 启动速度 | 🚀 快速 | ⚡ 极快 |

## 命令

### `chater chat`
开始一个聊天会话。

选项：
- `-m, --model <model>` - 指定要使用的模型

### `chater config`
配置API密钥和设置。

### `chater models`
列出所有可用的模型及其状态。

## 支持的模型

- **DeepSeek**: `deepseek-chat`, `deepseek-coder`
- **OpenAI**: `gpt-3.5-turbo`, `gpt-4`
- **Anthropic**: `claude-3-sonnet`

## 聊天命令

在聊天过程中，你可以使用以下命令：

- `/exit` - 退出聊天
- `/help` - 显示帮助信息
- `/clear` - 清除当前会话
- `/models` - 显示可用模型

## 配置文件

配置文件位置: `~/.chaterrc.json`

示例配置：
```json
{
  "defaultModel": "deepseek-chat",
  "apiKeys": {
    "deepseek": "your-deepseek-api-key",
    "openai": "your-openai-api-key"
  },
  "models": {
    "deepseek-chat": {
      "provider": "deepseek",
      "model": "deepseek-chat",
      "temperature": 0.7,
      "maxTokens": 2000
    }
  }
}
```

## 开发

```bash
# 克隆仓库
git clone https://github.com/yourusername/chater.git
cd chater

# 安装依赖
npm install

# 开发模式运行
npm run dev chat

# 构建
npm run build
```

## 许可证

MIT License

## 贡献

欢迎贡献代码！请先fork这个仓库，然后提交Pull Request。

## MCP (Model Context Protocol) 集成

Chater 现在支持 MCP，允许 AI 模型通过标准化协议访问外部工具和资源。

### MCP 功能特性

- 🔌 **多服务器支持**: 同时连接多个 MCP 服务器
- 🛠️ **工具集成**: 自动发现和集成 MCP 工具
- 📊 **状态监控**: 实时显示连接状态和可用工具
- 🔄 **自动重连**: 智能错误处理和连接恢复
- ⚙️ **灵活配置**: 支持 stdio、SSE、WebSocket 传输

### 配置 MCP 服务器

#### 添加 MCP 服务器
```bash
chater config mcp
```

选择"添加新的 MCP 服务器"，然后按提示配置：

1. **服务器 ID**: 唯一标识符
2. **服务器名称**: 显示名称
3. **传输类型**: stdio、SSE 或 WebSocket
4. **连接配置**: 根据传输类型配置命令或 URL

#### 管理 MCP 服务器
```bash
# 列出所有服务器状态
chater mcp --list

# 显示系统状态
chater mcp --status

# 测试特定服务器
chater mcp --test <server-id>

# 重新连接服务器
chater mcp --reconnect <server-id>

# 列出可用工具
chater mcp --tools
```

### MCP 服务器配置示例

#### Stdio 传输
```json
{
  "id": "filesystem",
  "name": "文件系统工具",
  "transport": "stdio",
  "connection": {
    "type": "stdio",
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/directory"]
  },
  "enabled": true
}
```

#### SSE 传输
```json
{
  "id": "web-search",
  "name": "网络搜索",
  "transport": "sse",
  "connection": {
    "type": "sse",
    "url": "http://localhost:3000/sse"
  },
  "enabled": true
}
```

#### WebSocket 传输
```json
{
  "id": "database",
  "name": "数据库查询",
  "transport": "websocket",
  "connection": {
    "type": "websocket",
    "url": "ws://localhost:3001/ws"
  },
  "enabled": true
}
```

### 使用 MCP 工具

在聊天中，MCP 工具会自动集成到 AI 的工具集中。工具名称格式为：
```
mcp_{server-id}_{tool-name}
```

例如：
- `mcp_filesystem_read_file` - 读取文件
- `mcp_web-search_search` - 网络搜索
- `mcp_database_query` - 数据库查询

### 故障排除

#### 连接问题
1. 检查服务器配置是否正确
2. 确认 MCP 服务器程序已安装
3. 查看连接日志：`chater mcp --status`

#### 工具不可用
1. 确认服务器已连接：`chater mcp --list`
2. 重新连接服务器：`chater mcp --reconnect <server-id>`
3. 检查工具列表：`chater mcp --tools`

### 开发自定义 MCP 服务器

参考 [MCP 官方文档](https://modelcontextprotocol.io/) 了解如何开发自定义 MCP 服务器。

## 支持

如果你遇到问题或有建议，请在GitHub上创建issue。