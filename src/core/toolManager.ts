import { Tool } from '../types';
import { MCPManager } from './mcpManager';
import { MCPToolWrapper } from '../types/mcp';
import { readFileSync, writeFileSync, existsSync, statSync, readdirSync, accessSync, constants } from 'fs';
import { join, resolve, basename, dirname } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * 工具管理器 - 管理任务执行中可用的工具
 */
export class ToolManager {
  private mcpManager?: MCPManager;

  constructor(mcpManager?: MCPManager) {
    this.mcpManager = mcpManager;
  }

  /**
   * 设置 MCP 管理器
   */
  public setMCPManager(mcpManager: MCPManager): void {
    this.mcpManager = mcpManager;
  }

  /**
   * 获取可用工具列表（包括 MCP 工具）
   */
  public getAvailableTools(): Tool[] {
    const builtinTools = this.getBuiltinTools();
    const mcpTools = this.getMCPTools();

    return [...builtinTools, ...mcpTools];
  }

  /**
   * 获取内置工具列表
   */
  private getBuiltinTools(): Tool[] {
    return [
      {
        name: 'read_file',
        description: '读取文件内容，支持文本文件和代码文件',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: '文件的相对或绝对路径' }
          },
          required: ['path']
        },
        execute: async (args: Record<string, any>) => this.readFile(args as { path: string })
      },
      {
        name: 'write_file',
        description: '写入内容到文件，如果文件不存在会自动创建',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: '文件的相对或绝对路径' },
            content: { type: 'string', description: '要写入的文件内容' }
          },
          required: ['path', 'content']
        },
        execute: async (args: Record<string, any>) => this.writeFile(args as { path: string; content: string })
      },
      {
        name: 'list_directory',
        description: '列出目录中的文件和子目录',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: '目录路径，默认为当前目录' }
          },
          required: []
        },
        execute: async (args: Record<string, any>) => this.listDirectory(args as { path?: string })
      },
      {
        name: 'file_info',
        description: '获取文件或目录的详细信息',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: '文件或目录路径' }
          },
          required: ['path']
        },
        execute: async (args: Record<string, any>) => this.getFileInfo(args as { path: string })
      },
      {
        name: 'execute_command',
        description: '执行系统命令（如git、npm等）',
        parameters: {
          type: 'object',
          properties: {
            command: { type: 'string', description: '要执行的命令' },
            cwd: { type: 'string', description: '执行命令的工作目录，默认为当前目录' }
          },
          required: ['command']
        },
        execute: async (args: Record<string, any>) => this.executeCommand(args as { command: string; cwd?: string })
      },
      {
        name: 'search_files',
        description: '在指定目录中搜索包含特定内容的文件',
        parameters: {
          type: 'object',
          properties: {
            pattern: { type: 'string', description: '搜索模式或关键词' },
            directory: { type: 'string', description: '搜索目录，默认为当前目录' },
            file_pattern: { type: 'string', description: '文件名模式，如 "*.js"' }
          },
          required: ['pattern']
        },
        execute: async (args: Record<string, any>) => this.searchFiles(args as { pattern: string; directory?: string; file_pattern?: string })
      },
      {
        name: 'check_permissions',
        description: '检查文件或目录的访问权限',
        parameters: {
          type: 'object',
          properties: {
            path: { type: 'string', description: '要检查权限的文件或目录路径' }
          },
          required: ['path']
        },
        execute: async (args: Record<string, any>) => this.checkPermissions(args as { path: string })
      }
    ];
  }

  /**
   * 获取工具名称列表
   */
  public getToolNames(): string[] {
    return this.getAvailableTools().map(tool => tool.name);
  }

  /**
   * 检查工具是否可用
   */
  public isToolAvailable(toolName: string): boolean {
    return this.getToolNames().includes(toolName);
  }

  /**
   * 执行工具调用
   */
  public async executeTool(toolName: string, parameters: any): Promise<any> {
    
    if (!this.isToolAvailable(toolName)) {
      return {
        success: false,
        error: `工具 ${toolName} 不可用`
      };
    }

    try {
      // 找到对应的工具并执行
      const tool = this.getAvailableTools().find(t => t.name === toolName);
      if (!tool) {
        return {
          success: false,
          error: `工具 ${toolName} 未找到`
        };
      }

      // 调用真实的工具执行方法
      const result = await tool.execute(parameters);
      return result;
      
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '工具执行失败'
      };
    }
  }

  /**
   * 读取文件内容
   */
  private async readFile(args: { path: string }): Promise<any> {
    try {
      console.log(`🔍 尝试读取文件: ${args.path}`);
      const filePath = resolve(args.path);
      console.log(`📍 解析后的绝对路径: ${filePath}`);
      
      if (!existsSync(filePath)) {
        console.log(`❌ 文件不存在: ${filePath}`);
        return {
          success: false,
          error: `文件不存在: ${args.path}`,
          resolvedPath: filePath
        };
      }

      console.log(`✅ 文件存在，检查权限...`);
      
      // 检查文件权限
      try {
        accessSync(filePath, constants.R_OK);
        console.log(`✅ 文件可读`);
      } catch (accessError) {
        console.log(`❌ 文件无读取权限:`, accessError);
        return {
          success: false,
          error: `无法读取文件，权限不足: ${args.path}`,
          resolvedPath: filePath,
          permissionError: true
        };
      }
      
      const stats = statSync(filePath);
      
      if (stats.isDirectory()) {
        console.log(`❌ 路径是目录，不是文件`);
        return {
          success: false,
          error: `指定路径是目录，不是文件: ${args.path}`,
          resolvedPath: filePath
        };
      }

      console.log(`📖 开始读取文件内容...`);
      const content = readFileSync(filePath, 'utf-8');
      console.log(`✅ 文件读取成功，长度: ${content.length} 字符`);
      
      return {
        success: true,
        path: args.path,
        resolvedPath: filePath,
        content,
        size: stats.size,
        lines: content.split('\n').length,
        encoding: 'utf-8'
      };
    } catch (error) {
      console.log(`❌ 读取文件异常:`, error);
      return {
        success: false,
        error: `读取文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: error instanceof Error ? error.stack : String(error)
      };
    }
  }

  /**
   * 写入文件内容
   */
  private async writeFile(args: { path: string; content: string }): Promise<any> {
    try {
      const filePath = resolve(args.path);
      
      // 确保目录存在
      const dir = dirname(filePath);
      if (!existsSync(dir)) {
        return {
          success: false,
          error: `目录不存在: ${dir}`
        };
      }

      writeFileSync(filePath, args.content, 'utf-8');
      
      return {
        success: true,
        path: args.path,
        message: `文件写入成功`,
        size: Buffer.byteLength(args.content, 'utf8')
      };
    } catch (error) {
      return {
        success: false,
        error: `写入文件失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 列出目录内容
   */
  private async listDirectory(args: { path?: string }): Promise<any> {
    try {
      const dirPath = resolve(args.path || '.');
      
      if (!existsSync(dirPath)) {
        return {
          success: false,
          error: `目录不存在: ${args.path || '.'}`
        };
      }

      const stats = statSync(dirPath);
      if (!stats.isDirectory()) {
        return {
          success: false,
          error: `指定路径不是目录: ${args.path || '.'}`
        };
      }

      const items = readdirSync(dirPath);
      const result = items.map(item => {
        const itemPath = join(dirPath, item);
        const itemStats = statSync(itemPath);
        return {
          name: item,
          type: itemStats.isDirectory() ? 'directory' : 'file',
          size: itemStats.isFile() ? itemStats.size : null,
          modified: itemStats.mtime
        };
      });

      return {
        success: true,
        path: args.path || '.',
        items: result,
        count: result.length
      };
    } catch (error) {
      return {
        success: false,
        error: `列出目录失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 获取文件信息
   */
  private async getFileInfo(args: { path: string }): Promise<any> {
    try {
      const filePath = resolve(args.path);
      
      if (!existsSync(filePath)) {
        return {
          success: false,
          error: `路径不存在: ${args.path}`
        };
      }

      const stats = statSync(filePath);
      
      return {
        success: true,
        path: args.path,
        absolutePath: filePath,
        type: stats.isDirectory() ? 'directory' : 'file',
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime,
        isReadable: true, // 简化版本
        isWritable: true  // 简化版本
      };
    } catch (error) {
      return {
        success: false,
        error: `获取文件信息失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 执行系统命令
   */
  private async executeCommand(args: { command: string; cwd?: string }): Promise<any> {
    try {
      const options: any = {};
      if (args.cwd) {
        options.cwd = resolve(args.cwd);
      }

      const { stdout, stderr } = await execAsync(args.command, options);
      
      return {
        success: true,
        command: args.command,
        stdout: String(stdout).trim(),
        stderr: String(stderr).trim(),
        cwd: options.cwd || process.cwd()
      };
    } catch (error: any) {
      return {
        success: false,
        command: args.command,
        error: error.message,
        stdout: error.stdout || '',
        stderr: error.stderr || '',
        exitCode: error.code
      };
    }
  }

  /**
   * 搜索文件内容
   */
  private async searchFiles(args: { pattern: string; directory?: string; file_pattern?: string }): Promise<any> {
    try {
      const searchDir = resolve(args.directory || '.');
      
      if (!existsSync(searchDir)) {
        return {
          success: false,
          error: `搜索目录不存在: ${args.directory || '.'}`
        };
      }

      // 使用 grep 命令进行搜索（如果可用）
      let command = `grep -r "${args.pattern}" "${searchDir}"`;
      if (args.file_pattern) {
        command = `grep -r --include="${args.file_pattern}" "${args.pattern}" "${searchDir}"`;
      }

      try {
        const { stdout } = await execAsync(command);
        const matches = stdout.trim().split('\n').filter(line => line.length > 0);
        
        return {
          success: true,
          pattern: args.pattern,
          directory: args.directory || '.',
          matches: matches.slice(0, 50), // 限制结果数量
          totalMatches: matches.length
        };
      } catch (grepError) {
        // 如果 grep 失败，返回简单的文件名搜索结果
        return {
          success: false,
          error: `搜索失败，请确保grep命令可用`,
          pattern: args.pattern
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `搜索失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    }
  }

  /**
   * 检查文件权限
   */
  private async checkPermissions(args: { path: string }): Promise<any> {
    try {
      const filePath = resolve(args.path);
      
      if (!existsSync(filePath)) {
        return {
          success: false,
          error: `路径不存在: ${args.path}`,
          resolvedPath: filePath
        };
      }

      const stats = statSync(filePath);
      const permissions = {
        readable: false,
        writable: false,
        executable: false
      };

      // 检查各种权限
      try {
        accessSync(filePath, constants.R_OK);
        permissions.readable = true;
      } catch {}

      try {
        accessSync(filePath, constants.W_OK);
        permissions.writable = true;
      } catch {}

      try {
        accessSync(filePath, constants.X_OK);
        permissions.executable = true;
      } catch {}

      // 获取详细权限信息
      const mode = stats.mode;
      const octalMode = (mode & parseInt('777', 8)).toString(8);
      
      return {
        success: true,
        path: args.path,
        resolvedPath: filePath,
        type: stats.isDirectory() ? 'directory' : 'file',
        permissions,
        mode: octalMode,
        uid: stats.uid,
        gid: stats.gid,
        size: stats.size,
        owner: {
          uid: stats.uid,
          gid: stats.gid
        },
        timestamps: {
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime
        }
      };
    } catch (error) {
      return {
        success: false,
        error: `检查权限失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: error instanceof Error ? error.stack : String(error)
      };
    }
  }

  /**
   * 获取工具描述
   */
  public getToolDescription(toolName: string): string {
    const tool = this.getAvailableTools().find(t => t.name === toolName);
    return tool ? tool.description : '未知工具';
  }

  /**
   * 获取 MCP 工具列表
   */
  private getMCPTools(): Tool[] {
    if (!this.mcpManager) {
      return [];
    }

    const mcpTools: Tool[] = [];
    const mcpToolWrappers = this.mcpManager.getAllTools();

    for (const wrapper of mcpToolWrappers) {
      const mcpTool: Tool = {
        name: `${wrapper.serverName.toLowerCase().replace(/[^a-z0-9]/g, '')}.${wrapper.tool.name}`,
        description: `[${wrapper.serverName}] ${wrapper.tool.description || wrapper.tool.name}`,
        parameters: wrapper.tool.inputSchema || {
          type: 'object',
          properties: {},
          required: []
        },
        execute: async (args: Record<string, any>) => {
          try {
            const result = await wrapper.call(args);

            // 转换 MCP 结果为工具执行结果
            if (result.isError) {
              return {
                success: false,
                error: result.content.map(c => c.text).join('\n') || 'MCP tool execution failed'
              };
            }

            return {
              success: true,
              result: result.content.map(c => {
                if (c.type === 'text') {
                  return c.text;
                } else if (c.type === 'image') {
                  return `[Image: ${c.mimeType || 'unknown'}]`;
                } else if (c.type === 'resource') {
                  return `[Resource: ${c.mimeType || 'unknown'}]`;
                }
                return '[Unknown content type]';
              }).join('\n')
            };
          } catch (error) {
            return {
              success: false,
              error: `MCP tool execution failed: ${error instanceof Error ? error.message : String(error)}`
            };
          }
        }
      };

      mcpTools.push(mcpTool);
    }

    return mcpTools;
  }

  /**
   * 检查工具是否为 MCP 工具
   */
  public isMCPTool(toolName: string): boolean {
    // 新的命名格式：serverName.toolName，不能以内置工具名开头
    const builtinToolNames = this.getBuiltinTools().map(t => t.name);
    return toolName.includes('.') && !builtinToolNames.includes(toolName);
  }

  /**
   * 获取 MCP 工具的服务器信息
   */
  public getMCPToolInfo(toolName: string): { serverId: string; toolName: string; serverName: string } | null {
    if (!this.isMCPTool(toolName)) {
      return null;
    }

    const dotIndex = toolName.indexOf('.');
    if (dotIndex === -1) {
      return null;
    }

    const serverName = toolName.substring(0, dotIndex);
    const originalToolName = toolName.substring(dotIndex + 1);

    // 查找对应的服务器ID
    if (!this.mcpManager) {
      return null;
    }

    const clients = this.mcpManager.getClients();
    const matchingClient = clients.find(c => 
      c.config.name.toLowerCase().replace(/[^a-z0-9]/g, '') === serverName
    );

    if (!matchingClient) {
      return null;
    }

    return { 
      serverId: matchingClient.config.id, 
      toolName: originalToolName,
      serverName: matchingClient.config.name
    };
  }

  /**
   * 获取 MCP 服务器状态摘要
   */
  public getMCPStatus(): {
    totalServers: number;
    connectedServers: number;
    totalTools: number;
    serverDetails: Array<{
      id: string;
      name: string;
      status: string;
      toolCount: number;
    }>;
  } {
    if (!this.mcpManager) {
      return {
        totalServers: 0,
        connectedServers: 0,
        totalTools: 0,
        serverDetails: []
      };
    }

    const clients = this.mcpManager.getClients();
    const connectedClients = clients.filter(c => c.status === 'connected');
    const totalTools = clients.reduce((sum, c) => sum + c.tools.length, 0);

    return {
      totalServers: clients.length,
      connectedServers: connectedClients.length,
      totalTools,
      serverDetails: clients.map(c => ({
        id: c.config.id,
        name: c.config.name,
        status: c.status,
        toolCount: c.tools.length
      }))
    };
  }
}