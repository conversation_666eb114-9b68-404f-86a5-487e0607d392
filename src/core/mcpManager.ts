import { EventEmitter } from 'events';
import { spawn, ChildProcess } from 'child_process';
import {
  MCPServerConfig,
  MCPClientInfo,
  MCPClientStatus,
  MCPEvent,
  MCPEventType,
  MCPManagerConfig,
  MCPToolWrapper,
  MCPConnectionStats,
  Tool,
  Resource,
  Prompt,
  CallToolRequest,
  CallToolResult,
  ListToolsResult,
  ListResourcesResult,
  ListPromptsResult,
  InitializeRequest,
  InitializeResult,
  ClientCapabilities,
  ServerCapabilities
} from '../types/mcp';

/**
 * MCP 客户端管理器
 * 负责管理多个 MCP 服务器连接的生命周期
 */
export class MCPManager extends EventEmitter {
  private clients: Map<string, MCPClient> = new Map();
  private config: MCPManagerConfig;
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(config: MCPManagerConfig) {
    super();
    this.config = config;
    this.startHealthCheck();
  }

  /**
   * 添加 MCP 服务器连接
   */
  async addServer(serverConfig: MCPServerConfig): Promise<void> {
    if (this.clients.has(serverConfig.id)) {
      throw new Error(`MCP server with id '${serverConfig.id}' already exists`);
    }

    if (this.clients.size >= this.config.maxConcurrentConnections) {
      throw new Error(`Maximum concurrent connections (${this.config.maxConcurrentConnections}) reached`);
    }

    const client = new MCPClient(serverConfig, this.config);
    
    // 监听客户端事件
    client.on('connected', () => this.emitEvent('client_connected', serverConfig.id));
    client.on('disconnected', () => this.emitEvent('client_disconnected', serverConfig.id));
    client.on('error', (error) => this.emitEvent('client_error', serverConfig.id, { error }));
    client.on('tool_called', (data) => this.emitEvent('tool_called', serverConfig.id, data));

    this.clients.set(serverConfig.id, client);

    if (serverConfig.enabled) {
      await client.connect();
    }
  }

  /**
   * 移除 MCP 服务器连接
   */
  async removeServer(serverId: string): Promise<void> {
    const client = this.clients.get(serverId);
    if (!client) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    await client.disconnect();
    this.clients.delete(serverId);
  }

  /**
   * 获取所有客户端信息
   */
  getClients(): MCPClientInfo[] {
    return Array.from(this.clients.values()).map(client => client.getInfo());
  }

  /**
   * 获取特定客户端信息
   */
  getClient(serverId: string): MCPClientInfo | undefined {
    const client = this.clients.get(serverId);
    return client?.getInfo();
  }

  /**
   * 获取所有可用工具
   */
  getAllTools(): MCPToolWrapper[] {
    const tools: MCPToolWrapper[] = [];
    
    for (const client of this.clients.values()) {
      const info = client.getInfo();
      if (info.status === 'connected') {
        for (const tool of info.tools) {
          tools.push({
            tool,
            serverId: info.config.id,
            serverName: info.config.name,
            call: (args) => client.callTool(tool.name, args)
          });
        }
      }
    }
    
    return tools;
  }

  /**
   * 调用工具
   */
  async callTool(serverId: string, toolName: string, args: Record<string, any>): Promise<CallToolResult> {
    const client = this.clients.get(serverId);
    if (!client) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    return await client.callTool(toolName, args);
  }

  /**
   * 连接所有启用的服务器
   */
  async connectAll(): Promise<void> {
    const promises = Array.from(this.clients.values())
      .filter(client => client.getInfo().config.enabled)
      .map(client => client.connect().catch(err => 
        console.warn(`Failed to connect to MCP server: ${err.message}`)
      ));
    
    await Promise.all(promises);
  }

  /**
   * 断开所有连接
   */
  async disconnectAll(): Promise<void> {
    const promises = Array.from(this.clients.values())
      .map(client => client.disconnect());
    
    await Promise.all(promises);
    
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
  }

  /**
   * 重新连接特定服务器
   */
  async reconnectServer(serverId: string): Promise<void> {
    const client = this.clients.get(serverId);
    if (!client) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    await client.reconnect();
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.config.healthCheckInterval > 0) {
      this.healthCheckTimer = setInterval(() => {
        this.performHealthCheck();
      }, this.config.healthCheckInterval);
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const healthCheckPromises = Array.from(this.clients.values()).map(async (client) => {
      const info = client.getInfo();

      // 检查是否需要重连
      if (info.config.enabled && this.config.autoReconnect) {
        if (info.status === 'error') {
          try {
            await this.attemptReconnection(client);
          } catch (error) {
            console.warn(`Health check reconnection failed for ${info.config.id}: ${error}`);
            this.emitEvent('client_health_check_failed', info.config.id, { error });
          }
        } else if (info.status === 'connected') {
          // 对已连接的客户端执行心跳检查
          try {
            await this.performHeartbeat(client);
          } catch (error) {
            console.warn(`Heartbeat failed for ${info.config.id}: ${error}`);
            // 心跳失败，标记为错误状态并尝试重连
            client.markAsError(error instanceof Error ? error.message : String(error));
          }
        }
      }
    });

    await Promise.allSettled(healthCheckPromises);
  }

  /**
   * 尝试重新连接客户端
   */
  private async attemptReconnection(client: MCPClient): Promise<void> {
    const info = client.getInfo();
    const retryConfig = info.config.retry || this.config.defaultRetry;

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        await client.reconnect();
        this.emitEvent('client_reconnected', info.config.id, { attempt });
        return;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < retryConfig.maxRetries) {
          const delay = Math.min(
            retryConfig.initialDelay * Math.pow(retryConfig.backoffMultiplier, attempt - 1),
            retryConfig.maxDelay
          );

          this.emitEvent('client_reconnect_attempt', info.config.id, {
            attempt,
            maxRetries: retryConfig.maxRetries,
            nextDelay: delay
          });

          await this.sleep(delay);
        }
      }
    }

    // 所有重试都失败了
    if (lastError) {
      this.emitEvent('client_reconnect_failed', info.config.id, {
        error: lastError,
        attempts: retryConfig.maxRetries
      });
      throw lastError;
    }
  }

  /**
   * 执行心跳检查
   */
  private async performHeartbeat(client: MCPClient): Promise<void> {
    // 使用标准的 tools/list 方法进行心跳检查
    try {
      await client.ping();
    } catch (error) {
      throw new Error(`Heartbeat failed: ${error}`);
    }
  }

  /**
   * 睡眠指定毫秒数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 发出事件
   */
  private emitEvent(type: MCPEventType, serverId: string, data?: any): void {
    const event: MCPEvent = {
      type,
      serverId,
      timestamp: new Date(),
      data
    };
    this.emit('mcp_event', event);
  }
}

/**
 * MCP 客户端类
 * 管理单个 MCP 服务器连接
 */
class MCPClient extends EventEmitter {
  private config: MCPServerConfig;
  private managerConfig: MCPManagerConfig;
  private status: MCPClientStatus = 'disconnected';
  private process?: ChildProcess;
  private capabilities?: ServerCapabilities;
  private tools: Tool[] = [];
  private resources: Resource[] = [];
  private prompts: Prompt[] = [];
  private stats: MCPConnectionStats;
  private lastConnected?: Date;
  private error?: string;
  private requestId = 0;
  private pendingRequests = new Map<number, { resolve: (value: any) => void; reject: (reason?: any) => void }>();
  private messageBuffer = ''; // 消息缓冲区，处理不完整的JSON消息

  constructor(config: MCPServerConfig, managerConfig: MCPManagerConfig) {
    super();
    this.config = config;
    this.managerConfig = managerConfig;
    this.stats = {
      connectCount: 0,
      reconnectCount: 0,
      toolCallCount: 0,
      resourceReadCount: 0,
      promptGetCount: 0,
      errorCount: 0
    };
  }

  /**
   * 连接到 MCP 服务器
   */
  async connect(): Promise<void> {
    if (this.status === 'connected' || this.status === 'connecting') {
      return;
    }

    this.status = 'connecting';
    this.clearError();

    const timeout = this.setConnectionTimeout(
      this.config.timeout || this.managerConfig.defaultTimeout
    );

    try {
      if (this.config.transport === 'stdio') {
        await this.connectStdio();
      } else {
        throw new Error(`Transport type '${this.config.transport}' not yet implemented`);
      }

      await this.initialize();
      await this.loadCapabilities();

      clearTimeout(timeout);
      this.status = 'connected';
      this.lastConnected = new Date();
      this.stats.connectCount++;
      this.emit('connected');

    } catch (error) {
      clearTimeout(timeout);
      this.handleConnectionError(
        error instanceof Error ? error : new Error(String(error)),
        'Connection failed'
      );
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.status === 'disconnected') {
      return;
    }

    this.status = 'disconnected';
    
    if (this.process) {
      this.process.kill();
      this.process = undefined;
    }

    this.emit('disconnected');
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<void> {
    await this.disconnect();
    this.stats.reconnectCount++;
    await this.connect();
  }

  /**
   * 获取客户端信息
   */
  getInfo(): MCPClientInfo {
    return {
      config: this.config,
      status: this.status,
      capabilities: this.capabilities,
      tools: this.tools,
      resources: this.resources,
      prompts: this.prompts,
      lastConnected: this.lastConnected,
      error: this.error,
      stats: { ...this.stats }
    };
  }

  /**
   * 调用工具
   */
  async callTool(toolName: string, args: Record<string, any>): Promise<CallToolResult> {
    if (this.status !== 'connected') {
      throw new Error(`Cannot call tool: client is ${this.status}`);
    }

    const request: CallToolRequest = {
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: args
      }
    };

    this.stats.toolCallCount++;
    this.emit('tool_called', { toolName, args });

    return await this.sendRequest(request);
  }

  /**
   * 建立 stdio 连接
   */
  private async connectStdio(): Promise<void> {
    if (this.config.connection.type !== 'stdio') {
      throw new Error('Invalid connection config for stdio transport');
    }

    const { command, args = [], cwd, env } = this.config.connection;
    
    this.process = spawn(command, args, {
      cwd: cwd || process.cwd(),
      env: { ...process.env, ...this.config.env, ...env },
      stdio: ['pipe', 'pipe', 'pipe']
    });

    this.process.on('error', (error) => {
      this.status = 'error';
      this.error = error.message;
      this.stats.errorCount++;
      this.emit('error', error);
    });

    this.process.on('exit', (_code) => {
      if (this.status === 'connected') {
        this.status = 'disconnected';
        this.emit('disconnected');
      }
    });

    // 设置消息处理
    if (this.process.stdout) {
      this.process.stdout.on('data', (data) => {
        this.handleMessage(data.toString());
      });
    }
  }

  /**
   * 初始化连接
   */
  private async initialize(): Promise<void> {
    const capabilities: ClientCapabilities = {
      tools: { listChanged: true },
      resources: { subscribe: true, listChanged: true },
      prompts: { listChanged: true }
    };

    const request: InitializeRequest = {
      method: 'initialize',
      params: {
        protocolVersion: '2025-06-18',
        capabilities,
        clientInfo: {
          name: 'chater',
          version: '0.3.3'
        }
      }
    };

    const result: InitializeResult = await this.sendRequest(request);
    this.capabilities = result.capabilities;
  }

  /**
   * 加载服务器能力
   */
  private async loadCapabilities(): Promise<void> {
    // 加载工具列表
    if (this.capabilities?.tools) {
      try {
        const toolsResult: ListToolsResult = await this.sendRequest({ method: 'tools/list' });
        this.tools = toolsResult.tools;
      } catch (error) {
        console.warn(`Failed to load tools: ${error}`);
      }
    }

    // 加载资源列表
    if (this.capabilities?.resources) {
      try {
        const resourcesResult: ListResourcesResult = await this.sendRequest({ method: 'resources/list' });
        this.resources = resourcesResult.resources;
      } catch (error) {
        console.warn(`Failed to load resources: ${error}`);
      }
    }

    // 加载提示列表
    if (this.capabilities?.prompts) {
      try {
        const promptsResult: ListPromptsResult = await this.sendRequest({ method: 'prompts/list' });
        this.prompts = promptsResult.prompts;
      } catch (error) {
        console.warn(`Failed to load prompts: ${error}`);
      }
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest(request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = ++this.requestId;
      const jsonrpcRequest = {
        jsonrpc: '2.0',
        id,
        ...request
      };

      this.pendingRequests.set(id, { resolve, reject });

      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(id);
        reject(new Error(`Request timeout after ${this.config.timeout || this.managerConfig.defaultTimeout}ms`));
      }, this.config.timeout || this.managerConfig.defaultTimeout);

      // 发送消息
      if (this.process?.stdin) {
        this.process.stdin.write(JSON.stringify(jsonrpcRequest) + '\n');
      } else {
        clearTimeout(timeout);
        this.pendingRequests.delete(id);
        reject(new Error('No active connection'));
      }

      // 清理超时
      this.pendingRequests.get(id)!.resolve = (result: any) => {
        clearTimeout(timeout);
        resolve(result);
      };
      this.pendingRequests.get(id)!.reject = (error: any) => {
        clearTimeout(timeout);
        reject(error);
      };
    });
  }

  /**
   * 处理收到的消息（增强版本，支持不完整消息处理）
   */
  private handleMessage(data: string): void {
    // 将新数据添加到缓冲区
    this.messageBuffer += data;
    
    // 按行分割消息
    const lines = this.messageBuffer.split('\n');
    
    // 保留最后一个可能不完整的行
    this.messageBuffer = lines.pop() || '';
    
    // 处理完整的消息行
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;
      
      this.processMessage(trimmedLine);
    }
  }

  /**
   * 处理单条消息
   */
  private processMessage(messageText: string): void {
    try {
      const message = JSON.parse(messageText);
      
      // 验证消息格式
      if (!this.isValidJSONRPCMessage(message)) {
        console.warn('Received invalid JSONRPC message:', message);
        return;
      }
      
      // 处理响应消息
      if (message.id && this.pendingRequests.has(message.id)) {
        const { resolve, reject } = this.pendingRequests.get(message.id)!;
        this.pendingRequests.delete(message.id);
        
        if (message.error) {
          reject(new Error(message.error.message || 'Unknown error'));
        } else {
          resolve(message.result);
        }
      }
      // 处理通知消息（可选）
      else if (!message.id && message.method) {
        this.handleNotification(message);
      }
    } catch (error) {
      console.warn(`Failed to parse MCP message: ${error}`, {
        messageText,
        error
      });
    }
  }

  /**
   * 验证JSONRPC消息格式
   */
  private isValidJSONRPCMessage(message: any): boolean {
    return (
      message &&
      typeof message === 'object' &&
      message.jsonrpc === '2.0' &&
      (message.id !== undefined || message.method !== undefined)
    );
  }

  /**
   * 处理服务器通知消息
   */
  private handleNotification(message: any): void {
    // 处理服务器发送的通知（如工具列表变更等）
    switch (message.method) {
      case 'notifications/tools/list_changed':
        this.refreshTools();
        break;
      case 'notifications/resources/list_changed':
        this.refreshResources();
        break;
      case 'notifications/prompts/list_changed':
        this.refreshPrompts();
        break;
      default:
        console.debug('Received unhandled notification:', message.method);
    }
  }

  /**
   * 刷新工具列表
   */
  private async refreshTools(): Promise<void> {
    if (this.capabilities?.tools) {
      try {
        const toolsResult: ListToolsResult = await this.sendRequest({ method: 'tools/list' });
        this.tools = toolsResult.tools;
        this.emit('tools_updated', this.tools);
      } catch (error) {
        console.warn(`Failed to refresh tools: ${error}`);
      }
    }
  }

  /**
   * 刷新资源列表
   */
  private async refreshResources(): Promise<void> {
    if (this.capabilities?.resources) {
      try {
        const resourcesResult: ListResourcesResult = await this.sendRequest({ method: 'resources/list' });
        this.resources = resourcesResult.resources;
        this.emit('resources_updated', this.resources);
      } catch (error) {
        console.warn(`Failed to refresh resources: ${error}`);
      }
    }
  }

  /**
   * 刷新提示列表
   */
  private async refreshPrompts(): Promise<void> {
    if (this.capabilities?.prompts) {
      try {
        const promptsResult: ListPromptsResult = await this.sendRequest({ method: 'prompts/list' });
        this.prompts = promptsResult.prompts;
        this.emit('prompts_updated', this.prompts);
      } catch (error) {
        console.warn(`Failed to refresh prompts: ${error}`);
      }
    }
  }

  /**
   * 标记客户端为错误状态
   */
  public markAsError(errorMessage: string): void {
    this.status = 'error';
    this.error = errorMessage;
    this.stats.errorCount++;
    this.emit('error', new Error(errorMessage));
  }

  /**
   * 发送心跳检查连接状态（使用标准的 tools/list 方法）
   */
  public async ping(): Promise<void> {
    if (this.status !== 'connected') {
      throw new Error('Client is not connected');
    }

    try {
      // 使用 tools/list 作为心跳检查，这是MCP协议标准方法
      const request = {
        method: 'tools/list',
        params: {}
      };

      await Promise.race([
        this.sendRequest(request),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Heartbeat timeout')), 5000)
        )
      ]);
    } catch (error) {
      this.markAsError(`Heartbeat failed: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * 获取连接统计信息
   */
  public getConnectionStats(): MCPConnectionStats {
    return { ...this.stats };
  }

  /**
   * 重置错误状态
   */
  public clearError(): void {
    if (this.status === 'error') {
      this.error = undefined;
    }
  }

  /**
   * 检查是否可以重连
   */
  public canReconnect(): boolean {
    return this.status === 'error' || this.status === 'disconnected';
  }

  /**
   * 获取最后错误信息
   */
  public getLastError(): string | undefined {
    return this.error;
  }

  /**
   * 设置连接超时
   */
  private setConnectionTimeout(timeoutMs: number): NodeJS.Timeout {
    return setTimeout(() => {
      if (this.status === 'connecting') {
        this.markAsError('Connection timeout');
        this.disconnect();
      }
    }, timeoutMs);
  }

  /**
   * 增强的错误处理
   */
  private handleConnectionError(error: Error, context: string): void {
    const errorMessage = `${context}: ${error.message}`;
    this.markAsError(errorMessage);

    // 记录详细错误信息
    console.error(`MCP Client Error [${this.config.id}] ${errorMessage}`, {
      config: this.config,
      stats: this.stats,
      stack: error.stack
    });
  }
}
