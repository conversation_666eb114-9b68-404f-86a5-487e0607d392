import { cosmiconfigSync } from 'cosmiconfig';
import { ChaterConfig } from '../types';
import { MCPServerConfig, MCPManagerConfig } from '../types/mcp';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';

const moduleName = 'chater';
const explorer = cosmiconfigSync(moduleName);

const defaultConfig: ChaterConfig = {
  defaultModel: 'auto',
  apiKeys: {},
  models: {
    'deepseek-chat': {
      provider: 'deepseek',
      model: 'deepseek-chat',
      temperature: 0.7,
      maxTokens: 2000
    },
    'deepseek-reasoner': {
      provider: 'deepseek',
      model: 'deepseek-reasoner',
      temperature: 0.7,
      maxTokens: 2000
    },
    'deepseek-coder': {
      provider: 'deepseek',
      model: 'deepseek-coder',
      temperature: 0.7,
      maxTokens: 2000
    },
    'gpt-3.5-turbo': {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2000
    },
    'gpt-4': {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2000
    },
    'claude-3-sonnet': {
      provider: 'anthropic',
      model: 'claude-3-sonnet-20240229',
      temperature: 0.7,
      maxTokens: 2000
    }
  },
  storage: {
    historyLimit: 50,
    sessionLimit: 10
  },
  mcp: {
    servers: [],
    settings: {
      defaultTimeout: 30000,
      defaultRetry: {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 10000,
        backoffMultiplier: 2
      },
      maxConcurrentConnections: 10,
      autoReconnect: true,
      healthCheckInterval: 60000
    }
  }
};

export class ConfigManager {
  private config: ChaterConfig;
  private configPath: string;

  constructor() {
    this.configPath = path.join(os.homedir(), '.chaterrc.json');
    this.config = this.loadConfig();
  }

  private loadConfig(): ChaterConfig {
    try {
      // 首先尝试从用户配置文件加载
      const result = explorer.search();
      if (result && result.config) {
        return { ...defaultConfig, ...result.config };
      }

      // 如果没有找到配置文件，检查是否存在默认配置文件
      if (fs.existsSync(this.configPath)) {
        const userConfig = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
        return { ...defaultConfig, ...userConfig };
      }

      // 创建默认配置文件
      this.saveConfig(defaultConfig);
      return defaultConfig;
    } catch (error) {
      console.warn('配置加载失败，使用默认配置:', error);
      return defaultConfig;
    }
  }

  public getConfig(): ChaterConfig {
    return this.config;
  }

  public updateConfig(updates: Partial<ChaterConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig(this.config);
  }

  public setApiKey(provider: keyof ChaterConfig['apiKeys'], key: string): void {
    this.config.apiKeys[provider] = key;
    this.saveConfig(this.config);
  }

  private saveConfig(config: ChaterConfig): void {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error('配置保存失败:', error);
    }
  }

  public getConfigPath(): string {
    return this.configPath;
  }

  // MCP 相关配置方法
  public getMCPServers(): MCPServerConfig[] {
    return this.config.mcp?.servers || [];
  }

  public getMCPSettings(): MCPManagerConfig {
    return this.config.mcp?.settings || defaultConfig.mcp!.settings;
  }

  public addMCPServer(serverConfig: MCPServerConfig): void {
    // 验证服务器配置
    this.validateMCPServerConfig(serverConfig);

    if (!this.config.mcp) {
      this.config.mcp = {
        servers: [],
        settings: defaultConfig.mcp!.settings
      };
    }

    // 检查是否已存在相同 ID 的服务器
    const existingIndex = this.config.mcp.servers.findIndex(s => s.id === serverConfig.id);
    if (existingIndex >= 0) {
      throw new Error(`MCP server with id '${serverConfig.id}' already exists`);
    }

    // 检查是否已存在相同名称的服务器
    const existingNameIndex = this.config.mcp.servers.findIndex(s => s.name === serverConfig.name);
    if (existingNameIndex >= 0) {
      throw new Error(`MCP server with name '${serverConfig.name}' already exists`);
    }

    this.config.mcp.servers.push(serverConfig);
    this.saveConfig(this.config);
  }

  public updateMCPServer(serverId: string, updates: Partial<MCPServerConfig>): void {
    if (!this.config.mcp) {
      throw new Error('No MCP configuration found');
    }

    const serverIndex = this.config.mcp.servers.findIndex(s => s.id === serverId);
    if (serverIndex < 0) {
      throw new Error(`MCP server with id '${serverId}' not found`);
    }

    this.config.mcp.servers[serverIndex] = {
      ...this.config.mcp.servers[serverIndex],
      ...updates
    };
    this.saveConfig(this.config);
  }

  public removeMCPServer(serverId: string): void {
    if (!this.config.mcp) {
      return;
    }

    const serverIndex = this.config.mcp.servers.findIndex(s => s.id === serverId);
    if (serverIndex >= 0) {
      this.config.mcp.servers.splice(serverIndex, 1);
      this.saveConfig(this.config);
    }
  }

  public updateMCPSettings(settings: Partial<MCPManagerConfig>): void {
    if (!this.config.mcp) {
      this.config.mcp = {
        servers: [],
        settings: defaultConfig.mcp!.settings
      };
    }

    this.config.mcp.settings = {
      ...this.config.mcp.settings,
      ...settings
    };
    this.saveConfig(this.config);
  }

  public getMCPServer(serverId: string): MCPServerConfig | undefined {
    return this.config.mcp?.servers.find(s => s.id === serverId);
  }

  public enableMCPServer(serverId: string, enabled: boolean): void {
    this.updateMCPServer(serverId, { enabled });
  }

  /**
   * 验证 MCP 服务器配置
   */
  private validateMCPServerConfig(config: MCPServerConfig): void {
    // 基础字段验证
    if (!config.id?.trim()) {
      throw new Error('Server ID is required and cannot be empty');
    }

    if (!config.name?.trim()) {
      throw new Error('Server name is required and cannot be empty');
    }

    // ID格式验证（只允许字母数字和连字符）
    if (!/^[a-zA-Z0-9\-_]+$/.test(config.id)) {
      throw new Error('Server ID must contain only letters, numbers, hyphens, and underscores');
    }

    // 传输类型验证
    if (!['stdio', 'sse', 'websocket'].includes(config.transport)) {
      throw new Error('Invalid transport type. Must be one of: stdio, sse, websocket');
    }

    // 连接配置验证
    this.validateConnectionConfig(config.connection, config.transport);

    // 可选字段验证
    if (config.timeout !== undefined && (config.timeout <= 0 || config.timeout > 300000)) {
      throw new Error('Timeout must be between 1 and 300000 milliseconds');
    }

    if (config.retry) {
      this.validateRetryConfig(config.retry);
    }
  }

  /**
   * 验证连接配置
   */
  private validateConnectionConfig(connection: any, transport: string): void {
    if (!connection || typeof connection !== 'object') {
      throw new Error('Connection configuration is required');
    }

    if (connection.type !== transport) {
      throw new Error(`Connection type must match transport type: ${transport}`);
    }

    switch (transport) {
      case 'stdio':
        if (!connection.command?.trim()) {
          throw new Error('Stdio connection requires a command');
        }
        if (connection.args && !Array.isArray(connection.args)) {
          throw new Error('Stdio args must be an array');
        }
        break;

      case 'sse':
      case 'websocket':
        if (!connection.url?.trim()) {
          throw new Error(`${transport} connection requires a URL`);
        }
        try {
          const url = new URL(connection.url);
          if (transport === 'websocket' && !url.protocol.startsWith('ws')) {
            throw new Error('WebSocket URL must use ws:// or wss:// protocol');
          }
          if (transport === 'sse' && !url.protocol.startsWith('http')) {
            throw new Error('SSE URL must use http:// or https:// protocol');
          }
        } catch (error) {
          throw new Error(`Invalid URL: ${error instanceof Error ? error.message : String(error)}`);
        }
        break;

      default:
        throw new Error(`Unsupported transport type: ${transport}`);
    }
  }

  /**
   * 验证重试配置
   */
  private validateRetryConfig(retry: any): void {
    if (typeof retry !== 'object') {
      throw new Error('Retry configuration must be an object');
    }

    if (retry.maxRetries !== undefined) {
      if (!Number.isInteger(retry.maxRetries) || retry.maxRetries < 0 || retry.maxRetries > 10) {
        throw new Error('maxRetries must be an integer between 0 and 10');
      }
    }

    if (retry.initialDelay !== undefined) {
      if (!Number.isInteger(retry.initialDelay) || retry.initialDelay < 100 || retry.initialDelay > 60000) {
        throw new Error('initialDelay must be an integer between 100 and 60000 milliseconds');
      }
    }

    if (retry.maxDelay !== undefined) {
      if (!Number.isInteger(retry.maxDelay) || retry.maxDelay < 1000 || retry.maxDelay > 300000) {
        throw new Error('maxDelay must be an integer between 1000 and 300000 milliseconds');
      }
    }

    if (retry.backoffMultiplier !== undefined) {
      if (typeof retry.backoffMultiplier !== 'number' || retry.backoffMultiplier < 1 || retry.backoffMultiplier > 5) {
        throw new Error('backoffMultiplier must be a number between 1 and 5');
      }
    }

    // 验证逻辑关系
    if (retry.initialDelay !== undefined && retry.maxDelay !== undefined && retry.initialDelay >= retry.maxDelay) {
      throw new Error('initialDelay must be less than maxDelay');
    }
  }
}