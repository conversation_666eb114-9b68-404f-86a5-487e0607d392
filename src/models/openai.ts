import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, AIMessage, SystemMessage } from 'langchain/schema';
import { ModelProvider, ChatMessage, Tool, ToolCall } from '../types';

export class OpenAIProvider implements ModelProvider {
  public name = 'OpenAI';
  private client: ChatOpenAI | null = null;

  constructor(
    private apiKey: string,
    private model: string = 'gpt-3.5-turbo',
    private temperature: number = 0.7,
    private maxTokens: number = 2000
  ) {
    if (this.isAvailable()) {
      this.client = new ChatOpenAI({
        openAIApiKey: this.apiKey,
        modelName: this.model,
        temperature: this.temperature,
        maxTokens: this.maxTokens,
      });
    }
  }

  public isAvailable(): boolean {
    return !!this.apiKey;
  }

  public supportsTools(): boolean {
    // OpenAI 支持 function calling
    return true;
  }

  public async chat(messages: ChatMessage[], tools?: Tool[]): Promise<{
    content: string;
    toolCalls?: ToolCall[];
  }> {
    if (!this.client) {
      throw new Error('OpenAI provider is not available. Please check your API key.');
    }

    try {
      const langChainMessages = messages.map(msg => {
        switch (msg.role) {
          case 'user':
            return new HumanMessage(msg.content);
          case 'assistant':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // 准备工具定义
      const toolDefinitions = tools?.map(tool => ({
        type: "function" as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters
        }
      }));

      const response = await this.client.call(langChainMessages, {
        tools: toolDefinitions || undefined
      });

      // 解析响应
      const content = response.content as string;
      const toolCalls: ToolCall[] = [];

      // 检查是否有工具调用（基础实现）
      if (response.tool_calls && response.tool_calls.length > 0) {
        for (const toolCall of response.tool_calls) {
          toolCalls.push({
            id: toolCall.id || `call_${Date.now()}`,
            name: toolCall.name,
            arguments: toolCall.args || {}
          });
        }
      }

      return {
        content,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      };
    } catch (error) {
      console.error('OpenAI API 调用失败:', error);
      throw new Error(`OpenAI API 错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}