import { ChatAnthropic } from '@langchain/anthropic';
import { HumanMessage, AIMessage, SystemMessage } from 'langchain/schema';
import { ModelProvider, ChatMessage, Tool, ToolCall } from '../types';

export class AnthropicProvider implements ModelProvider {
  public name = 'Anthropic';
  private client: ChatAnthropic | null = null;

  constructor(
    private apiKey: string,
    private model: string = 'claude-3-sonnet-20240229',
    private temperature: number = 0.7,
    private maxTokens: number = 2000
  ) {
    if (this.isAvailable()) {
      this.client = new ChatAnthropic({
        anthropicApiKey: this.apiKey,
        modelName: this.model,
        temperature: this.temperature,
        maxTokens: this.maxTokens,
      });
    }
  }

  public isAvailable(): boolean {
    return !!this.apiKey;
  }

  public supportsTools(): boolean {
    // Anthropic 的 Claude 支持 function calling
    return true;
  }

  public async chat(messages: ChatMessage[], tools?: Tool[]): Promise<{
    content: string;
    toolCalls?: ToolCall[];
  }> {
    if (!this.client) {
      throw new Error('Anthropic provider is not available. Please check your API key.');
    }

    try {
      const langChainMessages = messages.map(msg => {
        switch (msg.role) {
          case 'user':
            return new HumanMessage(msg.content);
          case 'assistant':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // 准备工具定义
      const toolDefinitions = tools?.map(tool => ({
        type: "function" as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters
        }
      }));

      const response = await this.client.call(langChainMessages, {
        tools: toolDefinitions || undefined
      });

      // 解析响应
      const content = response.content as string;
      const toolCalls: ToolCall[] = [];

      // 检查是否有工具调用（基础实现）
      if (response.tool_calls && response.tool_calls.length > 0) {
        for (const toolCall of response.tool_calls) {
          toolCalls.push({
            id: toolCall.id || `call_${Date.now()}`,
            name: toolCall.name,
            arguments: toolCall.args || {}
          });
        }
      }

      return {
        content,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      };
    } catch (error) {
      console.error('Anthropic API 调用失败:', error);
      throw new Error(`Anthropic API 错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}