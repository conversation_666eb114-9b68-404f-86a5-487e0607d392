import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { MCPManager } from '../src/core/mcpManager';
import { ConfigManager } from '../src/core/config';
import { ToolManager } from '../src/core/toolManager';
import { MCPServerConfig, MCPManagerConfig } from '../src/types/mcp';

describe('MCP Integration', () => {
  let mcpManager: MCPManager;
  let configManager: ConfigManager;
  let toolManager: ToolManager;
  let mockServerConfig: MCPServerConfig;
  let managerConfig: MCPManagerConfig;

  beforeEach(() => {
    // 创建测试配置
    managerConfig = {
      defaultTimeout: 5000,
      defaultRetry: {
        maxRetries: 2,
        initialDelay: 100,
        maxDelay: 1000,
        backoffMultiplier: 2
      },
      maxConcurrentConnections: 5,
      autoReconnect: true,
      healthCheckInterval: 10000
    };

    mockServerConfig = {
      id: 'test-server',
      name: 'Test MCP Server',
      description: 'A test MCP server',
      transport: 'stdio',
      connection: {
        type: 'stdio',
        command: 'echo',
        args: ['test']
      },
      enabled: true,
      timeout: 5000,
      retry: {
        maxRetries: 2,
        initialDelay: 100,
        maxDelay: 1000,
        backoffMultiplier: 2
      }
    };

    mcpManager = new MCPManager(managerConfig);
    configManager = new ConfigManager();
    toolManager = new ToolManager(mcpManager);
  });

  afterEach(async () => {
    if (mcpManager) {
      await mcpManager.disconnectAll();
    }
  });

  describe('MCPManager', () => {
    it('should initialize with correct configuration', () => {
      expect(mcpManager).toBeDefined();
      expect(mcpManager.getClients()).toHaveLength(0);
    });

    it('should add server configuration', async () => {
      await mcpManager.addServer(mockServerConfig);
      const clients = mcpManager.getClients();
      expect(clients).toHaveLength(1);
      expect(clients[0].config.id).toBe('test-server');
    });

    it('should prevent duplicate server IDs', async () => {
      await mcpManager.addServer(mockServerConfig);
      await expect(mcpManager.addServer(mockServerConfig))
        .rejects.toThrow('MCP server with id \'test-server\' already exists');
    });

    it('should remove server', async () => {
      await mcpManager.addServer(mockServerConfig);
      await mcpManager.removeServer('test-server');
      expect(mcpManager.getClients()).toHaveLength(0);
    });

    it('should handle maximum concurrent connections', async () => {
      const smallConfig = { ...managerConfig, maxConcurrentConnections: 1 };
      const smallManager = new MCPManager(smallConfig);

      await smallManager.addServer(mockServerConfig);
      
      const secondConfig = { ...mockServerConfig, id: 'test-server-2' };
      await expect(smallManager.addServer(secondConfig))
        .rejects.toThrow('Maximum concurrent connections (1) reached');

      await smallManager.disconnectAll();
    });
  });

  describe('ConfigManager MCP Integration', () => {
    it('should get empty MCP servers initially', () => {
      const servers = configManager.getMCPServers();
      expect(servers).toEqual([]);
    });

    it('should add MCP server configuration', () => {
      configManager.addMCPServer(mockServerConfig);
      const servers = configManager.getMCPServers();
      expect(servers).toHaveLength(1);
      expect(servers[0].id).toBe('test-server');
    });

    it('should update MCP server configuration', () => {
      configManager.addMCPServer(mockServerConfig);
      configManager.updateMCPServer('test-server', { name: 'Updated Name' });
      
      const server = configManager.getMCPServer('test-server');
      expect(server?.name).toBe('Updated Name');
    });

    it('should remove MCP server configuration', () => {
      configManager.addMCPServer(mockServerConfig);
      configManager.removeMCPServer('test-server');
      
      const servers = configManager.getMCPServers();
      expect(servers).toHaveLength(0);
    });

    it('should enable/disable MCP server', () => {
      configManager.addMCPServer(mockServerConfig);
      configManager.enableMCPServer('test-server', false);
      
      const server = configManager.getMCPServer('test-server');
      expect(server?.enabled).toBe(false);
    });

    it('should get MCP settings', () => {
      const settings = configManager.getMCPSettings();
      expect(settings).toBeDefined();
      expect(settings.defaultTimeout).toBeDefined();
      expect(settings.maxConcurrentConnections).toBeDefined();
    });

    it('should update MCP settings', () => {
      configManager.updateMCPSettings({ defaultTimeout: 15000 });
      const settings = configManager.getMCPSettings();
      expect(settings.defaultTimeout).toBe(15000);
    });
  });

  describe('ToolManager MCP Integration', () => {
    it('should initialize without MCP manager', () => {
      const toolManagerWithoutMCP = new ToolManager();
      expect(toolManagerWithoutMCP.getMCPStatus().totalServers).toBe(0);
    });

    it('should set MCP manager', () => {
      toolManager.setMCPManager(mcpManager);
      expect(toolManager.getMCPStatus()).toBeDefined();
    });

    it('should get MCP status', () => {
      const status = toolManager.getMCPStatus();
      expect(status).toHaveProperty('totalServers');
      expect(status).toHaveProperty('connectedServers');
      expect(status).toHaveProperty('totalTools');
      expect(status).toHaveProperty('serverDetails');
    });

    it('should identify MCP tools', () => {
      expect(toolManager.isMCPTool('mcp_server_tool')).toBe(true);
      expect(toolManager.isMCPTool('regular_tool')).toBe(false);
    });

    it('should parse MCP tool info', () => {
      const info = toolManager.getMCPToolInfo('mcp_test-server_example-tool');
      expect(info).toEqual({
        serverId: 'test-server',
        toolName: 'example-tool'
      });
    });

    it('should return null for invalid MCP tool names', () => {
      expect(toolManager.getMCPToolInfo('invalid_tool')).toBeNull();
      expect(toolManager.getMCPToolInfo('mcp_')).toBeNull();
    });

    it('should get all available tools including MCP tools', () => {
      const tools = toolManager.getAvailableTools();
      expect(Array.isArray(tools)).toBe(true);
      // 应该包含内置工具
      expect(tools.some(tool => tool.name === 'read_file')).toBe(true);
    });
  });

  describe('MCP Types', () => {
    it('should have correct server config structure', () => {
      expect(mockServerConfig).toHaveProperty('id');
      expect(mockServerConfig).toHaveProperty('name');
      expect(mockServerConfig).toHaveProperty('transport');
      expect(mockServerConfig).toHaveProperty('connection');
      expect(mockServerConfig).toHaveProperty('enabled');
    });

    it('should support different transport types', () => {
      const stdioConfig: MCPServerConfig = {
        ...mockServerConfig,
        transport: 'stdio',
        connection: { type: 'stdio', command: 'test' }
      };

      const sseConfig: MCPServerConfig = {
        ...mockServerConfig,
        id: 'sse-server',
        transport: 'sse',
        connection: { type: 'sse', url: 'http://localhost:3000/sse' }
      };

      const wsConfig: MCPServerConfig = {
        ...mockServerConfig,
        id: 'ws-server',
        transport: 'websocket',
        connection: { type: 'websocket', url: 'ws://localhost:3000/ws' }
      };

      expect(stdioConfig.connection.type).toBe('stdio');
      expect(sseConfig.connection.type).toBe('sse');
      expect(wsConfig.connection.type).toBe('websocket');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid server configuration', async () => {
      const invalidConfig = { ...mockServerConfig, id: '' };
      await expect(mcpManager.addServer(invalidConfig as any))
        .rejects.toThrow();
    });

    it('should handle non-existent server operations', async () => {
      await expect(mcpManager.removeServer('non-existent'))
        .rejects.toThrow('MCP server with id \'non-existent\' not found');

      await expect(mcpManager.reconnectServer('non-existent'))
        .rejects.toThrow('MCP server with id \'non-existent\' not found');
    });

    it('should handle configuration errors gracefully', () => {
      expect(() => configManager.updateMCPServer('non-existent', {}))
        .toThrow('MCP server with id \'non-existent\' not found');

      expect(() => configManager.addMCPServer(mockServerConfig))
        .not.toThrow();
      
      expect(() => configManager.addMCPServer(mockServerConfig))
        .toThrow('MCP server with id \'test-server\' already exists');
    });
  });

  describe('Integration Tests', () => {
    it('should integrate MCP manager with tool manager', async () => {
      toolManager.setMCPManager(mcpManager);
      await mcpManager.addServer(mockServerConfig);
      
      const status = toolManager.getMCPStatus();
      expect(status.totalServers).toBe(1);
    });

    it('should handle MCP events', (done) => {
      let eventReceived = false;
      
      mcpManager.on('mcp_event', (event) => {
        eventReceived = true;
        expect(event).toHaveProperty('type');
        expect(event).toHaveProperty('serverId');
        expect(event).toHaveProperty('timestamp');
        done();
      });

      // 触发事件
      mcpManager.addServer(mockServerConfig).catch(() => {
        // 连接可能失败，但应该触发事件
        if (!eventReceived) {
          done();
        }
      });
    });
  });
});
