{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(npx tsc:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(timeout 30 npm run dev chat)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git rm:*)", "<PERSON><PERSON>(chater:*)", "Bash(npm run build:*)", "mcp__filesystem__list_directory", "Bash(rg:*)", "mcp__zen__debug", "Bash(git commit:*)", "mcp__zen__chat", "WebFetch(domain:api-docs.deepseek.com)", "Bash(node:*)", "Bash(npm:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(nvm:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(yarn --version)", "Bash(yarn install)"], "deny": []}}